"""
Orchestration Team Session Manager for handling orchestration team sessions with human-in-the-loop functionality.
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

from autogen_agentchat.messages import TextMessage

from ...helper.session_manager import SessionManager
from ...kafka_client.producer import kafka_producer
from ...schemas.chat import AgentResponse, MessageType
from ...schemas.kafka import HumanInputRequest, MessageAttachment
from ...shared.config.base import get_settings
from ...utils.file_processor import FileProcessor
from ..message_processor import MessageProcessor
from .orchestrator_agent import build_orchestration_team

logger = logging.getLogger(__name__)


class OrchestrationSessionManager:
    """
    Manages orchestration team sessions with human-in-the-loop functionality.
    """

    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.settings = get_settings()
        self.file_processor = FileProcessor()
        self.message_processor = MessageProcessor()
        self.active_orchestration_sessions: Dict[str, Dict[str, Any]] = {}
        self.human_input_pending: Dict[str, asyncio.Event] = {}
        self.human_input_responses: Dict[str, str] = {}
        self._lock = asyncio.Lock()

    async def create_orchestration_session(
        self,
        user_id: str,
        organization_id: Optional[str] = None,
        variables: Optional[Dict[str, Any]] = None,
        chat_context: Optional[List[Dict[str, Any]]] = None,
    ) -> str:
        """
        Create a new orchestration team session.

        Args:
            user_id: ID of the user creating the session
            organization_id: Optional organization ID
            variables: Optional variables for the session
            chat_context: Optional chat context for the session

        Returns:
            Session ID for the orchestration team session
        """
        try:
            # Create a dummy agent config for session manager compatibility
            dummy_agent_config = {
                "name": "orchestration_team",
                "description": "Global orchestration team with human-in-the-loop",
                "agent_type": "orchestration_team",
                "system_message": "Orchestration team session",
            }

            # Create session in the session manager
            session_id = await self.session_manager.create_session(
                agent_config=dummy_agent_config,
                user_id=user_id,
                communication_type="orchestration_team",
                organization_id=organization_id,
                use_knowledge=False,
                agent_group_id="orchestration_team",
                variables=variables,
                chat_context=chat_context,
            )

            # Store orchestration session data (without team instance)
            # Team will be created fresh for each chat interaction
            async with self._lock:
                self.active_orchestration_sessions[session_id] = {
                    "user_id": user_id,
                    "organization_id": organization_id,
                    "variables": variables,
                    "created_at": datetime.utcnow().isoformat(),
                    "conversation_history": [],
                    "team_conversation_id": None,
                    "active_team_conversations": set(),  # Track active conversations
                }

            logger.info(f"Created orchestration session: {session_id}")
            return session_id

        except Exception as e:
            logger.error(f"Failed to create orchestration session: {str(e)}")
            raise

    async def _process_message_response(self, response: Any) -> Optional[AgentResponse]:
        """Process a single message response"""

        print(f"Processing response: {response}")

        msg_type: MessageType = self.message_processor.get_message_type(response)

        # Skip only task results and unknown types, process user input requests
        if msg_type in (MessageType.TASK_RESULT, MessageType.UNKNOWN):
            return AgentResponse(
                content="",
                source="assistant",
                models_usage=self.message_processor.extract_models_usage(response),
                message_type=msg_type.value,
                metadata=getattr(response, "metadata", None),
            )

        source, content = self.message_processor.extract_message_content(response)

        return AgentResponse(
            content=content,
            source=source,
            models_usage=self.message_processor.extract_models_usage(response),
            message_type=msg_type.value,
            metadata=getattr(response, "metadata", None),
        )

    async def process_orchestration_chat(
        self,
        session_id: str,
        user_message: str,
        run_id: str,
        attachments: Optional[List[MessageAttachment]] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a chat message with the orchestration team, including human-in-the-loop functionality.
        Supports both text and multimodal messages with attachments.

        Args:
            session_id: The session ID
            user_message: The user's message
            run_id: The run ID for this chat
            attachments: Optional list of file attachments (images, documents, etc.)

        Yields:
            Chat response chunks
        """
        try:
            async with self._lock:
                session_data = self.active_orchestration_sessions.get(session_id)

            if not session_data:

                yield {
                    "error": f"Orchestration session not found: {session_id}",
                    "success": False,
                    "final": True,
                }
                return

            # Create a fresh team instance for this conversation
            # This prevents "team is already running" errors
            try:
                team = await build_orchestration_team()
                logger.info(
                    f"Created fresh orchestration team for session: {session_id}"
                )
            except Exception as e:
                logger.error(f"Failed to create orchestration team: {str(e)}")
                yield {
                    "error": f"Failed to create orchestration team: {str(e)}",
                    "success": False,
                    "final": True,
                }
                return

            team_conversation_id = str(uuid.uuid4())

            # Update session with current conversation ID and track active conversation
            async with self._lock:
                self.active_orchestration_sessions[session_id][
                    "team_conversation_id"
                ] = team_conversation_id
                self.active_orchestration_sessions[session_id][
                    "active_team_conversations"
                ].add(team_conversation_id)

            # Process attachments and create appropriate message
            attachment_summary = ""
            if attachments:
                # Validate attachments first
                validation_errors = self.file_processor.validate_attachments(
                    attachments
                )
                if validation_errors:
                    error_msg = "Attachment validation failed: " + "; ".join(
                        validation_errors
                    )
                    logger.error(error_msg)
                    yield {
                        "error": error_msg,
                        "success": False,
                        "final": True,
                    }
                    return

                attachment_summary = self.file_processor.get_attachment_summary(
                    attachments
                )
                logger.info(
                    f"Processing orchestration chat with attachments: {attachment_summary}"
                )

            # Add user message to conversation history
            user_msg_entry = {
                "role": "user",
                "content": user_message,
                "timestamp": datetime.utcnow().isoformat(),
                "attachments": (
                    [att.model_dump() for att in attachments] if attachments else []
                ),
                "attachment_summary": attachment_summary,
            }

            async with self._lock:
                self.active_orchestration_sessions[session_id][
                    "conversation_history"
                ].append(user_msg_entry)

            # Update session memory
            await self.session_manager.update_session_memory(
                session_id=session_id,
                message=user_msg_entry,
            )

            # Yield initial response
            yield {
                "run_id": run_id,
                "session_id": session_id,
                "message": "Starting orchestration team conversation...",
                "success": True,
                "final": False,
                "team_conversation_id": team_conversation_id,
            }

            # Create appropriate message for the team (text or multimodal)
            try:
                if attachments:
                    # Create multimodal message with attachments
                    team_message = await self.file_processor.create_multimodal_message(
                        user_message, attachments, source="user"
                    )
                    logger.info(
                        f"Created multimodal message for orchestration team with {attachment_summary}"
                    )
                else:
                    # Create simple text message
                    team_message = TextMessage(content=user_message, source="user")

            except Exception as e:
                logger.error(f"Failed to create message for orchestration team: {e}")
                yield {
                    "error": f"Failed to process attachments: {e}",
                    "success": False,
                    "final": True,
                }
                return

            # Process with orchestration team
            async for response in team.run_stream(task=team_message):

                print("team response:", response)

                # Process response
                agent_response = await self._process_message_response(response)

                # Add to conversation history for non-empty responses
                if agent_response and agent_response.content.strip():
                    team_msg_entry = {
                        "role": agent_response.source,
                        "content": agent_response.content,
                        "agent": agent_response.source,
                        "timestamp": datetime.utcnow().isoformat(),
                        "message_type": agent_response.message_type,
                    }

                    async with self._lock:
                        self.active_orchestration_sessions[session_id][
                            "conversation_history"
                        ].append(team_msg_entry)

                    # Update session memory
                    await self.session_manager.update_session_memory(
                        session_id=session_id,
                        message=team_msg_entry,
                    )

                # Check if this is a user input request
                enable_human_input = agent_response.message_type == "user_input_request"

                if enable_human_input:
                    # Yield the user input request to the client
                    yield {
                        "run_id": run_id,
                        "session_id": session_id,
                        "agent_response": agent_response.to_dict(),
                        "success": True,
                        "final": False,
                        "team_conversation_id": team_conversation_id,
                        "human_input_requested": True,
                        "message": "Human input requested by the team",
                    }

                    # Request human input via Kafka
                    human_input = await self._request_human_input(
                        session_id=session_id,
                        run_id=run_id,
                        team_conversation_id=team_conversation_id,
                        prompt=(agent_response.content or "The team needs your input."),
                        context=self.active_orchestration_sessions[session_id][
                            "conversation_history"
                        ][-5:],
                        requesting_agent=agent_response.source,
                    )

                    if human_input:
                        # Add to conversation history
                        human_msg_entry = {
                            "role": "user",
                            "content": human_input,
                            "timestamp": datetime.utcnow().isoformat(),
                            "type": "human_input",
                        }

                        async with self._lock:
                            self.active_orchestration_sessions[session_id][
                                "conversation_history"
                            ].append(human_msg_entry)

                        # Update session memory
                        await self.session_manager.update_session_memory(
                            session_id=session_id,
                            message=human_msg_entry,
                        )

                        # Yield human input acknowledgment
                        yield {
                            "run_id": run_id,
                            "session_id": session_id,
                            "message": f"Human input received: {human_input}",
                            "success": True,
                            "final": False,
                            "team_conversation_id": team_conversation_id,
                            "human_input": human_input,
                        }

                        # Continue processing with the human input
                        # Create a new message with the human input
                        human_message = TextMessage(content=human_input, source="user")

                        # Continue the team conversation with human input
                        async for follow_up_response in team.run_stream(
                            task=human_message
                        ):
                            follow_up_agent_response = (
                                await self._process_message_response(follow_up_response)
                            )

                            if (
                                follow_up_agent_response
                                and follow_up_agent_response.content.strip()
                            ):
                                # Add to conversation history
                                follow_up_msg_entry = {
                                    "role": follow_up_agent_response.source,
                                    "content": follow_up_agent_response.content,
                                    "agent": follow_up_agent_response.source,
                                    "timestamp": datetime.utcnow().isoformat(),
                                    "message_type": (
                                        follow_up_agent_response.message_type
                                    ),
                                }

                                async with self._lock:
                                    session_data = self.active_orchestration_sessions[
                                        session_id
                                    ]
                                    conv_history = session_data["conversation_history"]
                                    conv_history.append(follow_up_msg_entry)

                                # Update session memory
                                await self.session_manager.update_session_memory(
                                    session_id=session_id,
                                    message=follow_up_msg_entry,
                                )

                            # Yield follow-up response
                            is_final = (
                                follow_up_agent_response.message_type == "task_result"
                            )
                            response_dict = follow_up_agent_response.to_dict()
                            yield {
                                "run_id": run_id,
                                "session_id": session_id,
                                "agent_response": response_dict,
                                "success": True,
                                "final": is_final,
                                "team_conversation_id": team_conversation_id,
                            }

                            if is_final:
                                return  # Exit the entire conversation
                    else:
                        # No human input received, continue without it
                        yield {
                            "run_id": run_id,
                            "session_id": session_id,
                            "message": "No human input received, continuing...",
                            "success": True,
                            "final": False,
                            "team_conversation_id": team_conversation_id,
                        }
                else:
                    # Regular response, yield it
                    yield {
                        "run_id": run_id,
                        "session_id": session_id,
                        "agent_response": agent_response.to_dict(),
                        "success": True,
                        "final": agent_response.message_type == "task_result",
                        "team_conversation_id": team_conversation_id,
                    }

                if agent_response.message_type == "task_result":
                    break

            # Clean up the team conversation from active tracking
            async with self._lock:
                if session_id in self.active_orchestration_sessions:
                    self.active_orchestration_sessions[session_id][
                        "active_team_conversations"
                    ].discard(team_conversation_id)

            yield {
                "run_id": run_id,
                "session_id": session_id,
                "message": "Orchestration team conversation completed",
                "success": True,
                "final": True,
                "team_conversation_id": team_conversation_id,
            }

        except Exception as e:
            logger.error(f"Error in orchestration chat processing: {str(e)}")

            # Clean up the team conversation from active tracking on error
            try:
                async with self._lock:
                    if session_id in self.active_orchestration_sessions:
                        self.active_orchestration_sessions[session_id][
                            "active_team_conversations"
                        ].discard(team_conversation_id)
            except Exception:
                pass  # Ignore cleanup errors

            yield {
                "run_id": run_id,
                "session_id": session_id,
                "error": f"Error: {str(e)}",
                "success": False,
                "final": True,
            }

    def _should_end_conversation(self, response_content: str) -> bool:
        """
        Determine if the conversation should end based on response content.

        Args:
            response_content: The response content to analyze

        Returns:
            True if conversation should end
        """
        end_triggers = [
            "TERMINATE",
            "task completed",
            "conversation complete",
            "final answer",
            "conclusion",
        ]

        content_lower = response_content.lower()
        return any(trigger.lower() in content_lower for trigger in end_triggers)

    async def _request_human_input(
        self,
        session_id: str,
        run_id: str,
        team_conversation_id: str,
        prompt: str,
        context: List[Dict[str, Any]],
        requesting_agent: Optional[str] = None,
        timeout_seconds: int = 300,
    ) -> Optional[str]:
        """
        Request human input via Kafka and wait for response.

        Args:
            session_id: The session ID
            run_id: The run ID
            team_conversation_id: The team conversation ID
            prompt: The prompt for human input
            context: Recent conversation context
            requesting_agent: The agent requesting input
            timeout_seconds: Timeout for human response

        Returns:
            Human input string or None if timeout
        """
        try:
            # Create human input request
            input_request = HumanInputRequest(
                session_id=session_id,
                run_id=run_id,
                team_conversation_id=team_conversation_id,
                prompt=prompt,
                context=context,
                timeout_seconds=timeout_seconds,
                requesting_agent=requesting_agent,
            )

            # Create event for waiting
            input_event = asyncio.Event()
            self.human_input_pending[team_conversation_id] = input_event

            # Send request via Kafka
            await kafka_producer.send_message(
                topic=self.settings.kafka.kafka_human_input_request_topic,
                message=input_request.model_dump(),
                headers=[
                    ("session_id", session_id.encode("utf-8")),
                    ("run_id", run_id.encode("utf-8")),
                    (
                        "team_conversation_id",
                        team_conversation_id.encode("utf-8"),
                    ),
                ],
            )

            # Wait for human input with timeout
            try:
                await asyncio.wait_for(input_event.wait(), timeout=timeout_seconds)

                # Get the response
                human_input = self.human_input_responses.get(team_conversation_id)

                # Clean up
                if team_conversation_id in self.human_input_pending:
                    del self.human_input_pending[team_conversation_id]
                if team_conversation_id in self.human_input_responses:
                    del self.human_input_responses[team_conversation_id]

                return human_input

            except asyncio.TimeoutError:
                logger.warning(
                    f"Human input timeout for conversation: " f"{team_conversation_id}"
                )

                # Clean up
                if team_conversation_id in self.human_input_pending:
                    del self.human_input_pending[team_conversation_id]

                return None

        except Exception as e:
            logger.error(f"Error requesting human input: {str(e)}")
            return None

    async def handle_human_input_response(
        self,
        team_conversation_id: str,
        user_input: str,
    ) -> bool:
        """
        Handle human input response.

        Args:
            team_conversation_id: The team conversation ID
            user_input: The human input

        Returns:
            True if handled successfully
        """
        try:
            if team_conversation_id in self.human_input_pending:
                # Store the response
                self.human_input_responses[team_conversation_id] = user_input

                # Signal the waiting coroutine
                self.human_input_pending[team_conversation_id].set()

                logger.info(
                    f"Human input received for conversation: " f"{team_conversation_id}"
                )
                return True
            else:
                logger.warning(
                    f"No pending input request for conversation: "
                    f"{team_conversation_id}"
                )
                return False

        except Exception as e:
            logger.error(f"Error handling human input response: {str(e)}")
            return False

    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get orchestration session information.

        Args:
            session_id: The session ID

        Returns:
            Session information or None if not found
        """
        async with self._lock:
            session_data = self.active_orchestration_sessions.get(session_id)
            if session_data:
                # Add runtime information
                session_data = session_data.copy()
                session_data["active_conversations_count"] = len(
                    session_data.get("active_team_conversations", set())
                )
            return session_data

    async def get_active_conversation_count(self, session_id: str) -> int:
        """
        Get the number of active team conversations for a session.

        Args:
            session_id: The session ID

        Returns:
            Number of active conversations
        """
        async with self._lock:
            session_data = self.active_orchestration_sessions.get(session_id)
            if session_data:
                active_convs = session_data.get("active_team_conversations", set())
                return len(active_convs)
            return 0

    async def cleanup_session(self, session_id: str) -> bool:
        """
        Clean up orchestration session.

        Args:
            session_id: The session ID to clean up

        Returns:
            True if cleaned up successfully
        """
        try:
            async with self._lock:
                if session_id in self.active_orchestration_sessions:
                    session_data = self.active_orchestration_sessions[session_id]
                    active_conversations = session_data.get(
                        "active_team_conversations", set()
                    )

                    if active_conversations:
                        logger.warning(
                            f"Cleaning up session {session_id} with {len(active_conversations)} active conversations"
                        )

                    # Clean up any pending human input for this session's conversations
                    for conv_id in active_conversations:
                        if conv_id in self.human_input_pending:
                            del self.human_input_pending[conv_id]
                        if conv_id in self.human_input_responses:
                            del self.human_input_responses[conv_id]

                    del self.active_orchestration_sessions[session_id]

            # Also clean up in the base session manager
            await self.session_manager.delete_session(session_id)

            logger.info(f"Cleaned up orchestration session: {session_id}")
            return True

        except Exception as e:
            logger.error(f"Error cleaning up orchestration session: {str(e)}")
            return False

    async def get_session_stats(self) -> Dict[str, Any]:
        """
        Get statistics about active orchestration sessions.

        Returns:
            Dictionary with session statistics
        """
        async with self._lock:
            total_sessions = len(self.active_orchestration_sessions)
            total_conversations = sum(
                len(session_data.get("active_team_conversations", set()))
                for session_data in self.active_orchestration_sessions.values()
            )
            pending_human_inputs = len(self.human_input_pending)

            return {
                "total_active_sessions": total_sessions,
                "total_active_conversations": total_conversations,
                "pending_human_inputs": pending_human_inputs,
                "sessions": {
                    session_id: {
                        "user_id": session_data.get("user_id"),
                        "created_at": session_data.get("created_at"),
                        "active_conversations": len(
                            session_data.get("active_team_conversations", set())
                        ),
                    }
                    for session_id, session_data in self.active_orchestration_sessions.items()
                },
            }
