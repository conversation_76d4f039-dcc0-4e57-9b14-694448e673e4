from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class MessageAttachment(BaseModel):
    """Schema for message attachments (images, documents, etc.)"""

    file_name: str = Field(..., description="Name of the attached file")
    file_type: str = Field(
        ..., description="MIME type of the file (e.g., 'image/jpeg', 'application/pdf')"
    )
    file_size: int = Field(..., description="Size of the file in bytes")
    file_data: Optional[str] = Field(
        None,
        description="Base64 encoded file content (use either file_data or file_url)",
    )
    file_url: Optional[str] = Field(
        None,
        description="URL to the file if stored externally (use either file_data or file_url)",
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata about the file"
    )

    def model_post_init(self, __context) -> None:
        """Validate that either file_data or file_url is provided, but not both"""
        if not self.file_data and not self.file_url:
            raise ValueError("Either file_data or file_url must be provided")
        if self.file_data and self.file_url:
            raise ValueError("Provide either file_data or file_url, not both")


class AgentCreationRequest(BaseModel):
    agent_id: str
    user_id: str
    communication_type: str
    run_id: str
    organization_id: Optional[str] = None
    use_knowledge: Optional[bool] = False
    agent_group_id: Optional[str] = None
    variables: Optional[dict] = None
    conversation_context: Optional[List[Dict[str, Any]]] = []


class AgentChatRequest(BaseModel):
    run_id: str
    session_id: str
    chat_context: list[dict[str, str]]
    chat_response: Optional[str] = "message"
    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )


class AgentChatResponse(BaseModel):
    run_id: str
    session_id: str
    event_type: Optional[str] = None
    agent_response: Optional[dict] = None
    success: bool = True
    message: Optional[str] = None
    final: Optional[bool] = False
    stream_chunk_id: Optional[int] = None  # For ordering streaming chunks
    timestamp: Optional[str] = None  # For timing information
    event_type: Optional[str] = None
    error: Optional[str] = None


class AgentMessageRequest(BaseModel):
    """
    Schema for direct agent message processing with provided agent config.
    This allows sending messages directly to agents without requiring
    pre-created sessions.
    """

    agent_config: Dict[str, Any]  # Configuration object for the agent
    run_id: str  # Unique identifier for the agent run session
    query: str  # The user's message/query to send to the agent
    user_id: str  # Identifier of the user sending the message
    variables: Optional[Dict[str, Any]] = None  # Variables for substitution
    organization_id: Optional[str] = None  # Organization identifier
    use_knowledge: Optional[bool] = False  # Whether to enable knowledge tools
    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )


class AgentSessionDeletionRequest(BaseModel):
    """
    Schema for agent session deletion requests.
    This allows deleting agent sessions once chat is complete.
    """

    session_id: str  # Session ID to delete
    run_id: str  # Unique identifier for the deletion request
    user_id: str  # Identifier of the user requesting deletion
    reason: Optional[str] = "chat_complete"  # Reason for deletion
    force: Optional[bool] = False  # Force deletion even if session is active


class AgentSessionDeletionResponse(BaseModel):
    """
    Schema for agent session deletion responses.
    """

    run_id: str  # Request identifier
    session_id: str  # Session that was deleted
    success: bool = True  # Whether deletion was successful
    message: Optional[str] = None  # Status message
    deleted_at: Optional[str] = None  # Timestamp of deletion


class HumanInputRequest(BaseModel):
    """
    Schema for requesting human input during team conversations.
    """

    session_id: str  # Session ID where input is needed
    run_id: str  # Current run ID
    team_conversation_id: str  # Unique ID for this team conversation
    prompt: str  # The prompt/question for the human
    context: List[Dict[str, Any]] = Field(
        default_factory=list, description="Recent conversation context"
    )
    timeout_seconds: Optional[int] = (
        300  # Timeout for human response (5 minutes default)
    )
    requesting_agent: Optional[str] = None  # Which agent is requesting input


class HumanInputResponse(BaseModel):
    """
    Schema for human input responses during team conversations.
    """

    session_id: str  # Session ID
    run_id: str  # Current run ID
    team_conversation_id: str  # Unique ID for this team conversation
    user_input: str  # The human's input/response
    timestamp: Optional[str] = None  # When the input was provided


class OrchestrationTeamSessionRequest(BaseModel):
    """
    Schema for creating orchestration team sessions.
    """

    run_id: str
    user_id: str
    organization_id: Optional[str] = None
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict)
    communication_type: str = "orchestration_team"


class OrchestrationTeamChatRequest(BaseModel):
    """
    Schema for orchestration team chat requests with human-in-the-loop support.
    """

    run_id: str
    session_id: Optional[str] = None  # If None, will create new session
    user_id: str  # Required for session creation if session_id is None
    user_message: str
    organization_id: Optional[str] = None
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict)
    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )
